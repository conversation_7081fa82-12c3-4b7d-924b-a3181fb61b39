const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// 导入路由
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const inventoryRoutes = require('./routes/inventory');
const chatRoutes = require('./routes/chat');
const returnRoutes = require('./routes/returns');
const fileRoutes = require('./routes/files');
const alertRoutes = require('./routes/alerts');
const policyRoutes = require('./routes/policy');
const reportRoutes = require('./routes/reports');
const stockCountRoutes = require('./routes/stockCount');
const chatService = require('./services/chatService');

// 导入中间件和服务
const errorHandler = require('./middleware/errorHandler');
const cronService = require('./services/cronService');
const logger = require('./utils/logger');

const app = express();
const server = createServer(app);

// Socket.io 配置
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// 基础中间件
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || "http://localhost:5173",
  credentials: true
}));

// 速率限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// 根路径 - API服务信息
app.get('/', (req, res) => {
  res.json({
    name: 'Chat-AI-3.0 库存管理与AI客服系统 API',
    version: '3.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    endpoints: {
      health: '/health',
      auth: '/api/auth/*',
      products: '/api/products/*',
      inventory: '/api/inventory/*',
      chat: '/api/chat/*',
      returns: '/api/returns/*',
      reports: '/api/reports/*',
      alerts: '/api/alerts/*',
      policy: '/api/policy/*',
      files: '/api/files/*',
      stockCount: '/api/stock-count/*'
    },
    documentation: 'https://github.com/HubGoodFood/Chat-AI-3.0',
    support: 'https://inventory-ai-frontend.onrender.com'
  });
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API 路由
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/inventory', inventoryRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/returns', returnRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/alerts', alertRoutes);
app.use('/api/policy', policyRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/stock-count', stockCountRoutes);

// Socket.io 连接处理
io.on('connection', (socket) => {
  logger.info(`新的客户端连接: ${socket.id}`);

  let currentUserId = null;
  let currentSessionId = null;

  // 用户加入聊天
  socket.on('join-chat', async (data) => {
    try {
      const { sessionId, userId, userToken } = data;

      // 验证用户身份（可选，根据需要实现）
      // const user = await verifyUserToken(userToken);

      currentUserId = userId;
      currentSessionId = sessionId;

      // 使用聊天服务处理用户连接
      await chatService.handleUserConnect(socket, userId, sessionId);

      logger.info(`用户 ${userId} 加入聊天会话: ${sessionId}`);

      // 通知会话中的其他用户
      socket.to(`session-${sessionId}`).emit('user-joined', {
        userId,
        sessionId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('用户加入聊天失败:', error);
      socket.emit('chat-error', {
        type: 'join_error',
        message: '加入聊天失败'
      });
    }
  });

  // 处理聊天消息
  socket.on('chat-message', async (data) => {
    try {
      // 添加用户ID到消息数据
      const messageData = {
        ...data,
        userId: currentUserId
      };

      // 使用聊天服务处理消息
      await chatService.handleChatMessage(socket, messageData);

    } catch (error) {
      logger.error('聊天消息处理错误:', error);
      socket.emit('chat-error', {
        type: 'message_error',
        message: '处理消息时发生错误'
      });
    }
  });

  // 用户正在输入
  socket.on('user-typing', (data) => {
    const { sessionId, isTyping } = data;
    socket.to(`session-${sessionId}`).emit('user-typing', {
      userId: currentUserId,
      sessionId,
      isTyping,
      timestamp: new Date().toISOString()
    });
  });

  // 获取聊天历史
  socket.on('get-chat-history', async (data) => {
    try {
      const { sessionId, limit = 50, offset = 0 } = data;

      const ChatSession = require('./models/ChatSession');
      const session = await ChatSession.findOne({ session_id: sessionId });

      if (session) {
        const messages = session.messages
          .slice(-limit - offset, -offset || undefined)
          .reverse();

        socket.emit('chat-history', {
          sessionId,
          messages,
          hasMore: session.messages.length > limit + offset
        });
      } else {
        socket.emit('chat-history', {
          sessionId,
          messages: [],
          hasMore: false
        });
      }

    } catch (error) {
      logger.error('获取聊天历史失败:', error);
      socket.emit('chat-error', {
        type: 'history_error',
        message: '获取聊天历史失败'
      });
    }
  });

  // 获取会话状态
  socket.on('get-session-status', async (data) => {
    try {
      const { sessionId } = data;
      const stats = chatService.getActiveSessionStats();

      socket.emit('session-status', {
        sessionId,
        isActive: true,
        stats
      });

    } catch (error) {
      logger.error('获取会话状态失败:', error);
    }
  });

  // 用户断开连接
  socket.on('disconnect', async () => {
    logger.info(`客户端断开连接: ${socket.id}`);

    if (currentUserId) {
      await chatService.handleUserDisconnect(socket, currentUserId);
    }
  });

  // 错误处理
  socket.on('error', (error) => {
    logger.error('Socket错误:', error);
  });
});

// 错误处理中间件
app.use(errorHandler);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    error: '接口不存在',
    path: req.originalUrl 
  });
});

// 数据库连接
mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/inventory_ai_db')
  .then(() => {
    logger.info('MongoDB 连接成功');
    logger.info(`数据库: ${mongoose.connection.name}`);

    // 启动定时任务服务
    cronService.start();
  })
  .catch((error) => {
    logger.error('MongoDB 连接失败:', error);
    logger.error('请检查数据库连接配置');
    process.exit(1);
  });

// 启动服务器
const PORT = process.env.PORT || 4000;
server.listen(PORT, () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  logger.info(`环境: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`前端地址: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到 SIGTERM 信号，开始优雅关闭...');

  // 停止定时任务服务
  cronService.stop();

  server.close(() => {
    logger.info('HTTP 服务器已关闭');
    mongoose.connection.close(false, () => {
      logger.info('MongoDB 连接已关闭');
      process.exit(0);
    });
  });
});

module.exports = { app, server, io };
