/**
 * 生产环境管理员用户创建脚本
 * 用于在生产环境中快速创建管理员账号
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../src/models/User');

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ 数据库连接成功');
    console.log(`📊 数据库: ${mongoose.connection.name}`);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
}

/**
 * 创建管理员用户
 */
async function createAdminUser() {
  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ username: 'admin' });
    
    if (existingAdmin) {
      console.log('⚠️  管理员用户已存在');
      console.log(`用户名: ${existingAdmin.username}`);
      console.log(`邮箱: ${existingAdmin.email}`);
      console.log(`角色: ${existingAdmin.role}`);
      console.log(`状态: ${existingAdmin.isActive ? '激活' : '禁用'}`);
      console.log(`创建时间: ${existingAdmin.createdAt}`);
      console.log(`最后登录: ${existingAdmin.lastLogin || '从未登录'}`);
      return existingAdmin;
    }

    // 创建新的管理员用户
    const adminUser = new User({
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      name: '系统管理员',
      role: 'admin',
      phone: '13800138000',
      isActive: true
    });

    // 设置管理员权限
    adminUser.setDefaultPermissions();

    // 保存用户
    await adminUser.save();

    console.log('✅ 管理员用户创建成功！');
    console.log('📋 用户信息:');
    console.log(`   用户名: ${adminUser.username}`);
    console.log(`   邮箱: ${adminUser.email}`);
    console.log(`   密码: admin123`);
    console.log(`   角色: ${adminUser.role}`);
    console.log(`   权限数量: ${adminUser.permissions.length}`);
    
    return adminUser;

  } catch (error) {
    console.error('❌ 创建管理员用户失败:', error);
    throw error;
  }
}

/**
 * 验证用户登录
 */
async function testUserLogin(username, password) {
  try {
    const user = await User.findOne({ username });
    
    if (!user) {
      console.log(`❌ 用户 ${username} 不存在`);
      return false;
    }

    const isPasswordValid = await user.comparePassword(password);
    
    if (isPasswordValid) {
      console.log(`✅ 用户 ${username} 密码验证成功`);
      return true;
    } else {
      console.log(`❌ 用户 ${username} 密码验证失败`);
      return false;
    }

  } catch (error) {
    console.error('❌ 密码验证过程出错:', error);
    return false;
  }
}

/**
 * 创建其他测试用户
 */
async function createTestUsers() {
  const testUsers = [
    {
      username: 'manager',
      email: '<EMAIL>',
      password: 'manager123',
      name: '仓库经理',
      role: 'manager',
      phone: '13800138001'
    },
    {
      username: 'staff1',
      email: '<EMAIL>',
      password: 'staff123',
      name: '仓库员工1',
      role: 'staff',
      phone: '13800138002'
    }
  ];

  const createdUsers = [];

  for (const userData of testUsers) {
    try {
      // 检查用户是否已存在
      const existingUser = await User.findOne({ username: userData.username });
      
      if (existingUser) {
        console.log(`⚠️  用户 ${userData.username} 已存在，跳过创建`);
        createdUsers.push(existingUser);
        continue;
      }

      // 创建新用户
      const user = new User({
        ...userData,
        isActive: true
      });

      user.setDefaultPermissions();
      await user.save();

      console.log(`✅ 用户 ${userData.username} 创建成功`);
      createdUsers.push(user);

    } catch (error) {
      console.error(`❌ 创建用户 ${userData.username} 失败:`, error);
    }
  }

  return createdUsers;
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 开始创建生产环境用户...');
    console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);
    
    // 连接数据库
    await connectDatabase();
    
    // 创建管理员用户
    const adminUser = await createAdminUser();
    
    // 创建其他测试用户
    const testUsers = await createTestUsers();
    
    // 验证用户登录
    console.log('\n🔐 验证用户登录...');
    await testUserLogin('admin', 'admin123');
    await testUserLogin('manager', 'manager123');
    await testUserLogin('staff1', 'staff123');
    
    // 显示用户统计
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    
    console.log('\n📊 用户统计:');
    console.log(`   总用户数: ${totalUsers}`);
    console.log(`   激活用户数: ${activeUsers}`);
    
    console.log('\n✅ 用户创建完成！');
    console.log('\n🔑 可用的登录账号:');
    console.log('   管理员: admin / admin123');
    console.log('   经理: manager / manager123');
    console.log('   员工: staff1 / staff123');
    
  } catch (error) {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('\n🔌 数据库连接已关闭');
    process.exit(0);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  createAdminUser,
  createTestUsers,
  testUserLogin
};
