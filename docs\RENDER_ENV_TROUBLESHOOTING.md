# 🔧 Render环境变量配置故障排除

## 📋 问题描述

后端服务构建成功但启动失败，退出状态码为1：

```
==> Build successful 🎉
==> Deploying...
==> Running 'npm start'
> inventory-ai-backend@1.0.0 start
> node src/server.js
==> Exited with status 1
```

## 🔍 问题分析

### 最可能的原因
1. **MongoDB连接失败** - `MONGO_URI` 环境变量未配置或配置错误
2. **JWT密钥缺失** - `JWT_SECRET` 环境变量未配置
3. **其他必需环境变量缺失**

### 诊断方法
在 Render Dashboard 中查看服务日志：
1. 进入后端服务页面
2. 点击 "Logs" 标签
3. 查看错误信息

## ✅ 解决方案

### 1. 配置必需的环境变量

在 Render Dashboard 的 Environment 页面中添加以下变量：

#### 🔴 必需配置（必须设置）
```bash
# 数据库连接（必需）
MONGO_URI=mongodb+srv://username:<EMAIL>/database_name

# JWT密钥（必需）
JWT_SECRET=71c255c5340f0bd7a75a6c8c6e1a376d7b904688f49696dbfcb480cfcc177417

# AI服务密钥（如果使用AI功能）
DEEPSEEK_API_KEY=your_deepseek_api_key
```

#### 🟡 可选配置（有默认值）
```bash
# AWS配置（如果使用文件上传）
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET=your_s3_bucket_name

# SMTP配置（如果使用邮件功能）
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
```

### 2. MongoDB Atlas 配置

如果使用 MongoDB Atlas：

1. **创建数据库用户**:
   - 进入 MongoDB Atlas Dashboard
   - 选择 Database Access
   - 创建新用户，记录用户名和密码

2. **配置网络访问**:
   - 选择 Network Access
   - 添加 IP 地址：`0.0.0.0/0`（允许所有IP）

3. **获取连接字符串**:
   - 选择 Clusters → Connect
   - 选择 "Connect your application"
   - 复制连接字符串
   - 替换 `<username>` 和 `<password>`

示例连接字符串：
```
mongodb+srv://prod_user:<EMAIL>/inventory_ai_prod?retryWrites=true&w=majority
```

### 3. 在 Render 中设置环境变量

1. **进入服务设置**:
   - 在 Render Dashboard 中选择后端服务
   - 点击 "Environment" 标签

2. **添加环境变量**:
   - 点击 "Add Environment Variable"
   - 输入变量名和值
   - 点击 "Save Changes"

3. **重新部署**:
   - 点击 "Manual Deploy"
   - 等待部署完成

## 🚀 快速修复步骤

### 步骤1: 最小配置
只配置最关键的环境变量：

```bash
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=71c255c5340f0bd7a75a6c8c6e1a376d7b904688f49696dbfcb480cfcc177417
```

### 步骤2: 验证配置
重新部署并检查日志：

```bash
# 期望看到的成功日志
MongoDB 连接成功
数据库: your_database_name
服务器运行在端口 4000
```

### 步骤3: 测试连接
```bash
curl https://inventory-ai-backend.onrender.com/health
```

期望返回：
```json
{
  "status": "ok",
  "timestamp": "2025-06-15T...",
  "uptime": 123.456,
  "environment": "production"
}
```

## 🔍 常见错误和解决方案

### 错误1: MongoDB连接超时
```
MongoDB 连接失败: MongooseServerSelectionError
```

**解决方案**:
- 检查 MONGO_URI 格式
- 确认数据库用户权限
- 检查网络访问设置

### 错误2: JWT密钥错误
```
Error: JWT secret is required
```

**解决方案**:
- 设置 JWT_SECRET 环境变量
- 使用提供的默认值或生成新的密钥

### 错误3: 端口冲突
```
Error: listen EADDRINUSE :::4000
```

**解决方案**:
- 确认 PORT 环境变量设置为 4000
- 检查是否有其他服务占用端口

## 📊 环境变量检查清单

- [ ] MONGO_URI - 数据库连接字符串
- [ ] JWT_SECRET - JWT签名密钥
- [ ] NODE_ENV - 设置为 production
- [ ] PORT - 设置为 4000
- [ ] FRONTEND_URL - 前端服务URL
- [ ] CORS_ORIGIN - CORS允许的源

## 🛠️ 调试工具

### 1. 查看详细日志
在 server.js 中临时添加调试信息：

```javascript
console.log('Environment variables check:');
console.log('MONGO_URI:', process.env.MONGO_URI ? 'Set' : 'Missing');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'Set' : 'Missing');
console.log('NODE_ENV:', process.env.NODE_ENV);
```

### 2. 测试数据库连接
创建简单的连接测试：

```javascript
const mongoose = require('mongoose');

mongoose.connect(process.env.MONGO_URI)
  .then(() => console.log('✅ Database connected'))
  .catch(err => console.error('❌ Database connection failed:', err));
```

## 📞 获取帮助

如果问题仍然存在：

1. **检查 Render 日志** - 查看详细错误信息
2. **验证环境变量** - 确保所有必需变量已设置
3. **测试数据库连接** - 使用 MongoDB Compass 或其他工具
4. **联系支持** - 提供错误日志和配置信息

---

**修复优先级**: 🔴 高优先级 - 影响服务启动
**预计修复时间**: 5-10分钟（配置环境变量）
