/**
 * CORS配置测试脚本
 * 用于验证生产环境的CORS配置是否正确
 */

const axios = require('axios');

// 配置
const BACKEND_URL = process.env.BACKEND_URL || 'https://inventory-ai-backend.onrender.com';
const FRONTEND_ORIGINS = [
  'https://inventory-ai-frontend.onrender.com',
  'http://localhost:5173',
  'http://localhost:3000',
  'https://example.com' // 这个应该被拒绝
];

/**
 * 测试CORS预检请求
 */
async function testCorsPreflightRequest(origin) {
  try {
    console.log(`🔍 测试CORS预检请求 - Origin: ${origin}`);
    
    const response = await axios.options(`${BACKEND_URL}/api/auth/login`, {
      headers: {
        'Origin': origin,
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      }
    });

    console.log(`✅ 预检请求成功 - Status: ${response.status}`);
    console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin']}`);
    console.log(`   Access-Control-Allow-Methods: ${response.headers['access-control-allow-methods']}`);
    console.log(`   Access-Control-Allow-Headers: ${response.headers['access-control-allow-headers']}`);
    console.log(`   Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials']}`);
    
    return true;

  } catch (error) {
    console.log(`❌ 预检请求失败 - Origin: ${origin}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data?.message || error.response.statusText}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

/**
 * 测试实际的API请求
 */
async function testActualApiRequest(origin) {
  try {
    console.log(`🔍 测试实际API请求 - Origin: ${origin}`);
    
    const response = await axios.post(`${BACKEND_URL}/api/auth/login`, {
      username: 'test',
      password: 'test'
    }, {
      headers: {
        'Origin': origin,
        'Content-Type': 'application/json'
      }
    });

    console.log(`✅ API请求成功 - Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
    
    return true;

  } catch (error) {
    if (error.response && error.response.status === 401) {
      // 401是预期的，因为我们使用了无效的凭据
      console.log(`✅ API请求成功（认证失败是预期的） - Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data).substring(0, 100)}...`);
      return true;
    } else {
      console.log(`❌ API请求失败 - Origin: ${origin}`);
      if (error.response) {
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Error: ${error.response.data?.message || error.response.statusText}`);
      } else {
        console.log(`   Error: ${error.message}`);
      }
      return false;
    }
  }
}

/**
 * 测试健康检查端点
 */
async function testHealthEndpoint() {
  try {
    console.log('🔍 测试健康检查端点...');
    
    const response = await axios.get(`${BACKEND_URL}/health`);
    
    console.log('✅ 健康检查成功');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(response.data, null, 2)}`);
    
    return true;

  } catch (error) {
    console.log('❌ 健康检查失败');
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Error: ${error.response.data}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

/**
 * 测试WebSocket连接
 */
async function testWebSocketConnection() {
  try {
    console.log('🔍 测试WebSocket连接信息...');
    
    // 检查Socket.io端点
    const response = await axios.get(`${BACKEND_URL}/socket.io/`);
    
    console.log('✅ Socket.io端点可访问');
    console.log(`   Status: ${response.status}`);
    
    return true;

  } catch (error) {
    console.log('❌ Socket.io端点测试失败');
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
    } else {
      console.log(`   Error: ${error.message}`);
    }
    return false;
  }
}

/**
 * 主测试函数
 */
async function runCorsTests() {
  console.log('🧪 开始CORS配置测试...');
  console.log(`🌍 后端URL: ${BACKEND_URL}`);
  console.log('=' * 60);

  // 1. 测试健康检查
  const healthOk = await testHealthEndpoint();
  if (!healthOk) {
    console.log('❌ 后端服务不可用，终止测试');
    return;
  }

  console.log('\n' + '=' * 60);

  // 2. 测试WebSocket连接
  await testWebSocketConnection();

  console.log('\n' + '=' * 60);

  // 3. 测试各个Origin的CORS配置
  const results = {
    preflight: {},
    api: {}
  };

  for (const origin of FRONTEND_ORIGINS) {
    console.log(`\n📍 测试Origin: ${origin}`);
    console.log('-' * 40);
    
    // 测试预检请求
    results.preflight[origin] = await testCorsPreflightRequest(origin);
    
    console.log(''); // 空行
    
    // 测试实际API请求
    results.api[origin] = await testActualApiRequest(origin);
    
    console.log(''); // 空行
  }

  console.log('\n' + '=' * 60);

  // 4. 测试结果总结
  console.log('📊 CORS测试结果总结:');
  console.log('');
  
  console.log('🔍 预检请求结果:');
  for (const [origin, success] of Object.entries(results.preflight)) {
    const status = success ? '✅ 通过' : '❌ 失败';
    console.log(`   ${origin}: ${status}`);
  }
  
  console.log('');
  console.log('🔍 API请求结果:');
  for (const [origin, success] of Object.entries(results.api)) {
    const status = success ? '✅ 通过' : '❌ 失败';
    console.log(`   ${origin}: ${status}`);
  }

  console.log('');
  
  // 检查生产环境Origin是否正常
  const prodOrigin = 'https://inventory-ai-frontend.onrender.com';
  const prodPreflightOk = results.preflight[prodOrigin];
  const prodApiOk = results.api[prodOrigin];
  
  if (prodPreflightOk && prodApiOk) {
    console.log('🎉 生产环境CORS配置正常！');
    console.log('✅ 前端应该能够正常访问后端API');
  } else {
    console.log('⚠️  生产环境CORS配置存在问题');
    console.log('🔧 建议检查后端CORS配置和环境变量');
  }

  console.log('');
  console.log('🔧 如果仍有问题，请检查:');
  console.log('   1. 后端环境变量 CORS_ORIGIN 和 FRONTEND_URL');
  console.log('   2. 后端代码中的CORS配置');
  console.log('   3. Render服务的环境变量设置');
  console.log('   4. 前端的API请求配置');
}

// 运行测试
if (require.main === module) {
  runCorsTests().catch(error => {
    console.error('❌ CORS测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testCorsPreflightRequest,
  testActualApiRequest,
  testHealthEndpoint,
  testWebSocketConnection
};
