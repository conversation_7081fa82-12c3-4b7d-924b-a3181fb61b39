/**
 * 认证API测试脚本
 * 用于测试生产环境的认证功能
 */

const axios = require('axios');

// 配置
const API_BASE_URL = process.env.API_BASE_URL || 'https://inventory-ai-backend.onrender.com';
const TEST_USERS = [
  { username: 'admin', password: 'admin123' },
  { username: 'manager', password: 'manager123' },
  { username: 'staff1', password: 'staff123' }
];

/**
 * 测试API端点连接
 */
async function testApiConnection() {
  try {
    console.log('🔗 测试API连接...');
    const response = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ API连接成功');
    console.log('📊 健康检查响应:', response.data);
    return true;
  } catch (error) {
    console.error('❌ API连接失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试用户登录
 */
async function testUserLogin(username, password) {
  try {
    console.log(`🔐 测试用户登录: ${username}`);
    
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      username,
      password
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log(`✅ 用户 ${username} 登录成功`);
      console.log('🎫 Token:', response.data.data.token.substring(0, 20) + '...');
      console.log('👤 用户信息:', {
        id: response.data.data.user.id,
        username: response.data.data.user.username,
        name: response.data.data.user.name,
        role: response.data.data.user.role,
        permissions: response.data.data.user.permissions.length + ' 个权限'
      });
      return response.data.data;
    } else {
      console.log(`❌ 用户 ${username} 登录失败:`, response.data.message);
      return null;
    }

  } catch (error) {
    console.error(`❌ 用户 ${username} 登录出错:`, error.message);
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误信息:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试用户注册
 */
async function testUserRegistration() {
  const newUser = {
    username: 'testuser_' + Date.now(),
    email: `test_${Date.now()}@hubgoodfood.com`,
    password: 'test123456',
    name: '测试用户',
    role: 'staff'
  };

  try {
    console.log('📝 测试用户注册...');
    
    const response = await axios.post(`${API_BASE_URL}/api/auth/register`, newUser, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ 用户注册成功');
      console.log('👤 新用户信息:', response.data.data.user);
      
      // 测试新用户登录
      console.log('🔐 测试新用户登录...');
      const loginResult = await testUserLogin(newUser.username, newUser.password);
      
      return loginResult ? newUser : null;
    } else {
      console.log('❌ 用户注册失败:', response.data.message);
      return null;
    }

  } catch (error) {
    console.error('❌ 用户注册出错:', error.message);
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误信息:', error.response.data);
    }
    return null;
  }
}

/**
 * 测试受保护的API端点
 */
async function testProtectedEndpoint(token) {
  try {
    console.log('🔒 测试受保护的API端点...');
    
    const response = await axios.get(`${API_BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.success) {
      console.log('✅ 受保护端点访问成功');
      console.log('👤 当前用户:', response.data.data);
      return true;
    } else {
      console.log('❌ 受保护端点访问失败:', response.data.message);
      return false;
    }

  } catch (error) {
    console.error('❌ 受保护端点访问出错:', error.message);
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误信息:', error.response.data);
    }
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🧪 开始认证API测试...');
  console.log(`🌍 API地址: ${API_BASE_URL}`);
  console.log('=' * 50);

  // 1. 测试API连接
  const isConnected = await testApiConnection();
  if (!isConnected) {
    console.log('❌ API连接失败，终止测试');
    return;
  }

  console.log('\n' + '=' * 50);

  // 2. 测试现有用户登录
  let successfulLogin = null;
  for (const user of TEST_USERS) {
    const loginResult = await testUserLogin(user.username, user.password);
    if (loginResult) {
      successfulLogin = loginResult;
      break;
    }
    console.log(''); // 空行分隔
  }

  console.log('\n' + '=' * 50);

  // 3. 如果没有现有用户能登录，尝试注册新用户
  if (!successfulLogin) {
    console.log('⚠️  现有用户登录失败，尝试注册新用户...');
    const newUser = await testUserRegistration();
    if (newUser) {
      successfulLogin = await testUserLogin(newUser.username, newUser.password);
    }
  }

  console.log('\n' + '=' * 50);

  // 4. 测试受保护的端点
  if (successfulLogin && successfulLogin.token) {
    await testProtectedEndpoint(successfulLogin.token);
  } else {
    console.log('⚠️  没有有效的token，跳过受保护端点测试');
  }

  console.log('\n' + '=' * 50);

  // 5. 测试结果总结
  console.log('📊 测试结果总结:');
  console.log(`   API连接: ${isConnected ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   用户登录: ${successfulLogin ? '✅ 成功' : '❌ 失败'}`);
  
  if (successfulLogin) {
    console.log('\n🎉 认证系统工作正常！');
    console.log('🔑 可用的登录凭据:');
    if (TEST_USERS.some(u => u.username === successfulLogin.user.username)) {
      console.log(`   用户名: ${successfulLogin.user.username}`);
      console.log(`   密码: ${TEST_USERS.find(u => u.username === successfulLogin.user.username).password}`);
    }
  } else {
    console.log('\n⚠️  认证系统存在问题，需要进一步调查');
    console.log('🔧 建议的解决步骤:');
    console.log('   1. 检查数据库连接');
    console.log('   2. 运行种子数据脚本');
    console.log('   3. 检查环境变量配置');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  testApiConnection,
  testUserLogin,
  testUserRegistration,
  testProtectedEndpoint
};
